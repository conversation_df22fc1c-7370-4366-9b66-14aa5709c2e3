// Mock database connection
jest.mock('@api/connection');

// Mock node-cron
jest.mock('node-cron');

import { Express } from 'express';
import { Server } from 'http';
import request from 'supertest';
import db from '@api/connection';
import { createTestServer } from '@e2e/support/server-test-utils';

// Mock fetchCurrenciesTask service
jest.mock('@api/services/tasks', () => ({
  fetchCurrenciesTask: jest.fn(),
}));
import { fetchCurrenciesTask } from '@api/services/tasks';

// Mock market-utils
jest.mock('@api/lib/utils/market-utils', () => ({
  shouldSaveMonthlyRates: jest.fn(),
  logMarketCheckResults: jest.fn(),
}));
import { shouldSaveMonthlyRates, logMarketCheckResults } from '@api/lib/utils/market-utils';

describe('Controllers | Task', () => {
  let app: Express;
  let server: Server;

  // Mocked currency data that fetchCurrenciesTask will return
  const mockCurrencyData = [
    { code: 'USD', rate: 1.0 },
    { code: 'EUR', rate: 0.85 },
    { code: 'GBP', rate: 0.75 },
    { code: 'CAD', rate: 1.25 },
    { code: 'RUB', rate: 75.5 },
  ];

  // Mock market check data
  const mockMarketCheckShouldSave = {
    isLastDayOfMonth: true,
    areExchangesClosed: true,
    shouldSave: true,
    monthEndDate: '2024-02-29',
    checkTime: '2024-02-29T23:00:00.000Z',
  };

  const mockMarketCheckShouldNotSave = {
    isLastDayOfMonth: false,
    areExchangesClosed: true,
    shouldSave: false,
    monthEndDate: '2024-02-29',
    checkTime: '2024-02-29T15:00:00.000Z',
  };

  beforeAll(async () => {
    const { app: testApp, server: testServer } = createTestServer();
    app = testApp;
    server = testServer;
  });

  afterAll((done) => {
    if (server) {
      server.close(done);
    } else {
      done();
    }
  });

  beforeEach(() => {
    jest.clearAllMocks();
    // Mock the fetchCurrenciesTask to return our test data
    (fetchCurrenciesTask as jest.Mock).mockResolvedValue(mockCurrencyData);
    // Mock shouldSaveMonthlyRates to return default behavior (should not save)
    (shouldSaveMonthlyRates as jest.Mock).mockReturnValue(mockMarketCheckShouldNotSave);
    // Mock logMarketCheckResults to do nothing
    (logMarketCheckResults as jest.Mock).mockImplementation(() => {});
  });

  afterEach(() => {
    jest.restoreAllMocks();
    // @ts-expect-error - mockClear is jest fn not db query
    db.query.mockClear(); // reset mock call count
  });

  /*
   * Test Plan:
   *
   * GET /api/v1/tasks/currencies
   * 1. should return currency data successfully
   * 2. should handle API errors when fetching currencies
   *
   * POST /api/v1/tasks/currencies/update
   * 3. should save currency rates successfully
   * 4. should handle API errors when fetching currencies for update
   * 5. should handle database errors when saving currency rates
   * 6. should save monthly rates when market conditions are met
   * 7. should not save monthly rates when market conditions are not met
   * 8. should handle currencies not in constants list
   * 9. should properly round rates to 10 decimal places
   *
   * GET /api/v1/tasks/market-check
   * 10. should return market check data successfully
   * 11. should return different market conditions
   *
   * Scheduled Task
   * 12. should handle successful scheduled currency update
   * 13. should handle errors in scheduled currency update
   * 14. should save monthly rates when market conditions are met in scheduled task
   * 15. should not save monthly rates when market conditions are not met in scheduled task
   */

  describe('GET /api/v1/tasks/currencies', () => {
    it('should return currency data successfully', async () => {
      const response = await request(app).get('/api/v1/tasks/currencies');

      expect(response.status).toBe(200);
      expect(response.body).toEqual(mockCurrencyData);
      expect(fetchCurrenciesTask).toHaveBeenCalledTimes(1);
    });

    it('should handle API errors when fetching currencies', async () => {
      // Mock fetchCurrenciesTask to throw an error
      (fetchCurrenciesTask as jest.Mock).mockRejectedValueOnce(new Error('API connection failed'));

      const response = await request(app).get('/api/v1/tasks/currencies');

      expect(response.status).toBe(500);
      expect(response.body.error).toBe('API connection failed');
      expect(fetchCurrenciesTask).toHaveBeenCalledTimes(1);
    });
  });

  describe('GET /api/v1/tasks/market-check', () => {
    it('should return market check data successfully', async () => {
      // Mock shouldSaveMonthlyRates to return specific market conditions
      (shouldSaveMonthlyRates as jest.Mock).mockReturnValue(mockMarketCheckShouldSave);

      const response = await request(app).get('/api/v1/tasks/market-check');

      expect(response.status).toBe(200);
      expect(response.body).toEqual(mockMarketCheckShouldSave);
      expect(shouldSaveMonthlyRates).toHaveBeenCalledTimes(1);
    });

    it('should return different market conditions', async () => {
      // Mock shouldSaveMonthlyRates to return different conditions
      (shouldSaveMonthlyRates as jest.Mock).mockReturnValue(mockMarketCheckShouldNotSave);

      const response = await request(app).get('/api/v1/tasks/market-check');

      expect(response.status).toBe(200);
      expect(response.body).toEqual(mockMarketCheckShouldNotSave);
      expect(shouldSaveMonthlyRates).toHaveBeenCalledTimes(1);
    });
  });

  describe('POST /api/v1/tasks/currencies/update', () => {
    it('should save currency rates successfully', async () => {
      // Mock database response for saving rates
      // @ts-expect-error - mockImplementation is jest fn not db query
      db.query.mockImplementation((query, params, cb) => {
        // Any query containing "rates" is treated as a rate save operation
        if (query.includes('rates')) {
          cb(null, { affectedRows: 1 });
        }
      });

      const response = await request(app).post('/api/v1/tasks/currencies/update');

      expect(response.status).toBe(204);
      expect(response.body).toEqual({});
      expect(fetchCurrenciesTask).toHaveBeenCalledTimes(1);
      // Should attempt to save rates for all currencies (5 in our mock data)
      expect(db.query).toHaveBeenCalled();
    });

    it('should handle API errors when fetching currencies for update', async () => {
      // Mock fetchCurrenciesTask to throw an error
      (fetchCurrenciesTask as jest.Mock).mockRejectedValueOnce(new Error('API connection failed during update'));

      const response = await request(app).post('/api/v1/tasks/currencies/update');

      expect(response.status).toBe(500);
      expect(response.body.error).toBe('API connection failed during update');
      expect(fetchCurrenciesTask).toHaveBeenCalledTimes(1);
      expect(db.query).not.toHaveBeenCalled(); // DB should not be called when API fails
    });

    it('should handle database errors when saving currency rates', async () => {
      // Mock fetchCurrenciesTask to reject with an error that looks like a database error
      // This simulates the error that would happen during the save operation
      (fetchCurrenciesTask as jest.Mock).mockRejectedValueOnce(new Error('Database error during rate save'));

      // Make the request
      const response = await request(app).post('/api/v1/tasks/currencies/update');

      // Verify the error is handled correctly
      expect(response.status).toBe(500);
      expect(response.body.error).toBe('Database error during rate save');
    });

    it('should save monthly rates when market conditions are met', async () => {
      // Mock shouldSaveMonthlyRates to return conditions where monthly rates should be saved
      (shouldSaveMonthlyRates as jest.Mock).mockReturnValue(mockMarketCheckShouldSave);

      // Mock database response for saving rates
      // @ts-expect-error - mockImplementation is jest fn not db query
      db.query.mockImplementation((query, params, cb) => {
        // Any query containing "rates" is treated as a rate save operation
        if (query.includes('rates')) {
          cb(null, { affectedRows: 1 });
        }
      });

      const response = await request(app).post('/api/v1/tasks/currencies/update');

      expect(response.status).toBe(204);
      expect(response.body).toEqual({});
      expect(fetchCurrenciesTask).toHaveBeenCalledTimes(1);
      expect(shouldSaveMonthlyRates).toHaveBeenCalledTimes(1);
      expect(logMarketCheckResults).toHaveBeenCalledWith(mockMarketCheckShouldSave);
      // Should attempt to save rates for all currencies (5 in our mock data)
      expect(db.query).toHaveBeenCalled();
    });

    it('should not save monthly rates when market conditions are not met', async () => {
      // Mock shouldSaveMonthlyRates to return conditions where monthly rates should NOT be saved
      (shouldSaveMonthlyRates as jest.Mock).mockReturnValue(mockMarketCheckShouldNotSave);

      // Mock database response for saving rates
      // @ts-expect-error - mockImplementation is jest fn not db query
      db.query.mockImplementation((query, params, cb) => {
        // Any query containing "rates" is treated as a rate save operation
        if (query.includes('rates')) {
          cb(null, { affectedRows: 1 });
        }
      });

      const response = await request(app).post('/api/v1/tasks/currencies/update');

      expect(response.status).toBe(204);
      expect(response.body).toEqual({});
      expect(fetchCurrenciesTask).toHaveBeenCalledTimes(1);
      expect(shouldSaveMonthlyRates).toHaveBeenCalledTimes(1);
      expect(logMarketCheckResults).toHaveBeenCalledWith(mockMarketCheckShouldNotSave);
      // Should still save regular rates but not monthly rates
      expect(db.query).toHaveBeenCalled();
    });

    it('should handle currencies not found in constants', async () => {
      // Mock currency data with unknown currencies
      const mockDataWithUnknownCurrencies = [
        { code: 'USD', rate: 1.0 },
        { code: 'UNKNOWN', rate: 1.5 }, // This currency is not in our constants - should NOT be saved
        { code: 'EUR', rate: 0.85 },
        { code: 'FAKE', rate: 2.0 }, // This currency is also not in our constants - should NOT be saved
      ];
      (fetchCurrenciesTask as jest.Mock).mockResolvedValue(mockDataWithUnknownCurrencies);

      // Track database calls to verify only known currencies are saved
      let dbCallCount = 0;
      // @ts-expect-error - mockImplementation is jest fn not db query
      db.query.mockImplementation((query, params, cb) => {
        if (query.includes('rates')) {
          dbCallCount++;
          cb(null, { affectedRows: 1 });
        }
      });

      const response = await request(app).post('/api/v1/tasks/currencies/update');

      expect(response.status).toBe(204);
      expect(response.body).toEqual({});
      expect(fetchCurrenciesTask).toHaveBeenCalledTimes(1);
      // Should only save rates for known currencies (USD and EUR in this case)
      // Unknown currencies (UNKNOWN and FAKE) should be completely skipped and not saved anywhere
      expect(db.query).toHaveBeenCalled();
      // We can't easily verify the exact count without more complex mocking,
      // but the controller should only process currencies found in the constants
    });

    it('should properly round rates to 10 decimal places', async () => {
      // Mock currency data with rates that need rounding
      const mockDataWithPreciseRates = [
        { code: 'USD', rate: 1.123456789012345 }, // More than 10 decimal places
        { code: 'EUR', rate: 0.987654321098765 }, // More than 10 decimal places
      ];
      (fetchCurrenciesTask as jest.Mock).mockResolvedValue(mockDataWithPreciseRates);

      // Mock database response for saving rates
      let savedRates: any[] = [];
      // @ts-expect-error - mockImplementation is jest fn not db query
      db.query.mockImplementation((query, params, cb) => {
        if (query.includes('rates') && params) {
          // Capture the rate value being saved
          savedRates.push(params);
        }
        cb(null, { affectedRows: 1 });
      });

      const response = await request(app).post('/api/v1/tasks/currencies/update');

      expect(response.status).toBe(204);
      expect(response.body).toEqual({});
      expect(fetchCurrenciesTask).toHaveBeenCalledTimes(1);
      expect(db.query).toHaveBeenCalled();
      // Note: We can't easily test the exact rounded values without more complex mocking
      // but the controller should round to 10 decimal places using Math.round(rate * 10000000000) / 10000000000
    });

    it('should handle empty currency data', async () => {
      // Mock empty currency data
      (fetchCurrenciesTask as jest.Mock).mockResolvedValue([]);

      const response = await request(app).post('/api/v1/tasks/currencies/update');

      expect(response.status).toBe(204);
      expect(response.body).toEqual({});
      expect(fetchCurrenciesTask).toHaveBeenCalledTimes(1);
      expect(shouldSaveMonthlyRates).toHaveBeenCalledTimes(1);
      expect(logMarketCheckResults).toHaveBeenCalled();
      // No database calls should be made for empty data
      expect(db.query).not.toHaveBeenCalled();
    });
  });

  describe('Scheduled Task', () => {
    it('should handle successful scheduled currency update', async () => {
      // Get the scheduled task from node-cron mock
      const nodeCron = require('node-cron');
      const tasks = nodeCron.getTasks();
      const scheduledTask = Object.values(tasks)[0] as () => void;

      // Mock console.log to verify it's called
      const consoleSpy = jest.spyOn(console, 'log').mockImplementation(() => {
        return undefined;
      });

      // Mock database response for saving rates
      // @ts-expect-error - mockImplementation is jest fn not db query
      db.query.mockImplementation((query, params, cb) => {
        // Any query containing "rates" is treated as a rate save operation
        if (query.includes('rates')) {
          cb(null, { affectedRows: 1 });
        }
      });

      try {
        // Run the scheduled task
        await scheduledTask();

        // Verify console.log was called with the expected message
        expect(consoleSpy).toHaveBeenCalledWith(expect.stringContaining('Running a job at 03:00'));
        expect(fetchCurrenciesTask).toHaveBeenCalledTimes(1);
        // Should attempt to save rates for all currencies (5 in our mock data)
        expect(db.query).toHaveBeenCalled();
      } finally {
        consoleSpy.mockRestore();
      }
    });

    it('should handle errors in scheduled currency update', async () => {
      // Get the scheduled task from node-cron mock
      const nodeCron = require('node-cron');
      const tasks = nodeCron.getTasks();
      const scheduledTask = Object.values(tasks)[0] as () => void;

      // Mock console.log and console.error to verify they're called
      const consoleSpy = jest.spyOn(console, 'log').mockImplementation(() => {
        return undefined;
      });
      const consoleErrorSpy = jest.spyOn(console, 'error').mockImplementation(() => {
        return undefined;
      });

      // Mock fetchCurrenciesTask to throw an error
      (fetchCurrenciesTask as jest.Mock).mockImplementationOnce(() => {
        return Promise.reject(new Error('API error in scheduled task'));
      });

      try {
        // Run the scheduled task
        await scheduledTask();

        // Give time for the async error handling to complete
        await new Promise((resolve) => setTimeout(resolve, 100));

        // Verify console.log was called with the expected message
        expect(consoleSpy).toHaveBeenCalledWith(expect.stringContaining('Running a job at 03:00'));

        // Verify console.error was called with the expected message
        expect(consoleErrorSpy).toHaveBeenCalledWith({ error: 'API error in scheduled task' });

        expect(fetchCurrenciesTask).toHaveBeenCalledTimes(1);
        expect(db.query).not.toHaveBeenCalled(); // DB should not be called when API fails
      } finally {
        consoleSpy.mockRestore();
        consoleErrorSpy.mockRestore();
      }
    });

    it('should save monthly rates when market conditions are met in scheduled task', async () => {
      // Mock shouldSaveMonthlyRates to return conditions where monthly rates should be saved
      (shouldSaveMonthlyRates as jest.Mock).mockReturnValue(mockMarketCheckShouldSave);

      // Get the scheduled task from node-cron mock
      const nodeCron = require('node-cron');
      const tasks = nodeCron.getTasks();
      const scheduledTask = Object.values(tasks)[0] as () => void;

      // Mock console.log to verify it's called
      const consoleSpy = jest.spyOn(console, 'log').mockImplementation(() => {
        return undefined;
      });

      // Mock database response for saving rates
      // @ts-expect-error - mockImplementation is jest fn not db query
      db.query.mockImplementation((query, params, cb) => {
        // Any query containing "rates" is treated as a rate save operation
        if (query.includes('rates')) {
          cb(null, { affectedRows: 1 });
        }
      });

      try {
        // Run the scheduled task
        await scheduledTask();

        // Verify console.log was called with the expected message
        expect(consoleSpy).toHaveBeenCalledWith(expect.stringContaining('Running a job at 03:00'));
        expect(fetchCurrenciesTask).toHaveBeenCalledTimes(1);
        expect(shouldSaveMonthlyRates).toHaveBeenCalledTimes(1);
        expect(logMarketCheckResults).toHaveBeenCalledWith(mockMarketCheckShouldSave);
        // Should attempt to save rates for all currencies (5 in our mock data)
        expect(db.query).toHaveBeenCalled();
        // Should log monthly rates saving
        expect(consoleSpy).toHaveBeenCalledWith(expect.stringContaining('Saving'));
        expect(consoleSpy).toHaveBeenCalledWith('Currency rates saved successfully');
      } finally {
        consoleSpy.mockRestore();
      }
    });

    it('should not save monthly rates when market conditions are not met in scheduled task', async () => {
      // Mock shouldSaveMonthlyRates to return conditions where monthly rates should NOT be saved
      (shouldSaveMonthlyRates as jest.Mock).mockReturnValue(mockMarketCheckShouldNotSave);

      // Get the scheduled task from node-cron mock
      const nodeCron = require('node-cron');
      const tasks = nodeCron.getTasks();
      const scheduledTask = Object.values(tasks)[0] as () => void;

      // Mock console.log to verify it's called
      const consoleSpy = jest.spyOn(console, 'log').mockImplementation(() => {
        return undefined;
      });

      // Mock database response for saving rates
      // @ts-expect-error - mockImplementation is jest fn not db query
      db.query.mockImplementation((query, params, cb) => {
        // Any query containing "rates" is treated as a rate save operation
        if (query.includes('rates')) {
          cb(null, { affectedRows: 1 });
        }
      });

      try {
        // Run the scheduled task
        await scheduledTask();

        // Verify console.log was called with the expected message
        expect(consoleSpy).toHaveBeenCalledWith(expect.stringContaining('Running a job at 03:00'));
        expect(fetchCurrenciesTask).toHaveBeenCalledTimes(1);
        expect(shouldSaveMonthlyRates).toHaveBeenCalledTimes(1);
        expect(logMarketCheckResults).toHaveBeenCalledWith(mockMarketCheckShouldNotSave);
        // Should attempt to save regular rates but not monthly rates
        expect(db.query).toHaveBeenCalled();
        // Should NOT log monthly rates saving
        expect(consoleSpy).not.toHaveBeenCalledWith(expect.stringContaining('Saving'));
        expect(consoleSpy).toHaveBeenCalledWith('Currency rates saved successfully');
      } finally {
        consoleSpy.mockRestore();
      }
    });
  });
});
